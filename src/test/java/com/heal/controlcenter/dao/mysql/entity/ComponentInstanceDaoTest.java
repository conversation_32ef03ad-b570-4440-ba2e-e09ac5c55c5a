package com.heal.controlcenter.dao.mysql.entity;

import com.heal.controlcenter.pojo.ClusterInstanceServiceDetails;
import com.heal.controlcenter.pojo.InstanceClusterServicePojo;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.core.RowMapper;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class ComponentInstanceDaoTest {

    @Mock
    private JdbcTemplate jdbcTemplate;

    @InjectMocks
    private ComponentInstanceDao componentInstanceDao;

    @Test
    void testGetInstanceClusterServiceDetails() throws SQLException {
        // Arrange
        int serviceId = 1;
        int accountId = 100;
        
        ClusterInstanceServiceDetails expectedDetails = ClusterInstanceServiceDetails.builder()
                .clusterId(1)
                .clusterName("Test Cluster")
                .clusterIdentifier("test-cluster-001")
                .serviceId(1)
                .serviceName("Test Service")
                .serviceIdentifier("test-service-001")
                .clusterComponentId(10)
                .clusterComponentName("Test Component")
                .clusterComponentTypeId(5)
                .clusterComponentTypeName("Test Component Type")
                .clusterComponentVersionId(2)
                .clusterComponentVersionName("v1.0")
                .clusterCommonVersionId(3)
                .clusterCommonVersionName("Common v1.0")
                .instanceId(20)
                .instanceName("Test Instance")
                .instanceIdentifier("test-instance-001")
                .hostAddress("*************")
                .instanceComponentId(15)
                .instanceComponentName("Instance Component")
                .instanceComponentTypeId(6)
                .instanceComponentTypeName("Instance Component Type")
                .instanceComponentVersionId(4)
                .instanceComponentVersionName("Instance v1.0")
                .instanceCommonVersionId(7)
                .instanceCommonVersionName("Instance Common v1.0")
                .build();

        List<ClusterInstanceServiceDetails> expectedList = Arrays.asList(expectedDetails);

        // Mock the jdbcTemplate.query method
        when(jdbcTemplate.query(anyString(), any(RowMapper.class), eq(accountId), eq(serviceId)))
                .thenReturn(expectedList);

        // Act
        List<InstanceClusterServicePojo> result = componentInstanceDao.getInstanceClusterServiceDetails(serviceId, accountId);

        // Assert
        assertNotNull(result);
        assertEquals(1, result.size());

        InstanceClusterServicePojo actualDetails = result.get(0);
        assertEquals(expectedDetails.getClusterId(), actualDetails.getClusterId());
        assertEquals(expectedDetails.getClusterName(), actualDetails.getClusterName());
        assertEquals(expectedDetails.getClusterIdentifier(), actualDetails.getClusterIdentifier());
        assertEquals(expectedDetails.getServiceId(), actualDetails.getServiceId());
        assertEquals(expectedDetails.getServiceName(), actualDetails.getServiceName());
        assertEquals(expectedDetails.getServiceIdentifier(), actualDetails.getServiceIdentifier());
        assertEquals(expectedDetails.getClusterComponentId(), actualDetails.getClusterComponentId());
        assertEquals(expectedDetails.getInstanceId(), actualDetails.getInstanceId());
        assertEquals(expectedDetails.getInstanceName(), actualDetails.getInstanceName());
        assertEquals(expectedDetails.getHostAddress(), actualDetails.getHostAddress());

        // Verify the SQL query was called with correct parameters
        verify(jdbcTemplate, times(1)).query(anyString(), any(RowMapper.class), eq(accountId), eq(serviceId));
    }

    @Test
    void testGetInstanceClusterServiceDetailsEmptyResult() {
        // Arrange
        int serviceId = 1;
        int accountId = 100;

        when(jdbcTemplate.query(anyString(), any(RowMapper.class), eq(accountId), eq(serviceId)))
                .thenReturn(Arrays.asList());

        // Act
        List<InstanceClusterServicePojo> result = componentInstanceDao.getInstanceClusterServiceDetails(serviceId, accountId);

        // Assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    void testGetInstanceClusterServiceDetailsException() {
        // Arrange
        int serviceId = 1;
        int accountId = 100;

        when(jdbcTemplate.query(anyString(), any(RowMapper.class), eq(accountId), eq(serviceId)))
                .thenThrow(new RuntimeException("Database error"));

        // Act
        List<InstanceClusterServicePojo> result = componentInstanceDao.getInstanceClusterServiceDetails(serviceId, accountId);

        // Assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }
}
