package com.heal.controlcenter.dao.mysql.entity;

import com.heal.controlcenter.exception.HealControlCenterException;
import com.heal.controlcenter.pojo.GroupKpiAttributeMapping;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * This implementation uses Spring JDBC with JdbcTemplate for database operations.
 */
@Slf4j
@Repository
public class BindInDao {

    @Autowired
    private JdbcTemplate jdbcTemplate;

    /**
     * Gets group KPI attribute mapping
     * @param accountId Account ID used to filter by account
     * @param instanceId Component instance ID
     * @return List of GroupKpiAttributeMapping
     * @throws HealControlCenterException if error occurs during database operation
     */
    public List<GroupKpiAttributeMapping> getGroupKpiAttributeMapping(int accountId, int instanceId) throws HealControlCenterException {
        String sql = "SELECT ckgd.attribute_value AS attributeValue, ckgd.mst_kpi_details_id AS kpiId, " +
                     "IFNULL(ckgd.alias_name, ckgd.attribute_value) AS aliasName " +
                     "FROM comp_instance_kpi_group_details ckgd " +
                     "JOIN comp_instance ci ON ci.id = ckgd.comp_instance_id " +
                     "WHERE ci.account_id = ? AND ckgd.comp_instance_id = ?";

        try {
            log.debug("Fetching group kpi attribute mapping details for accountId [{}] and instanceId [{}]", accountId, instanceId);

            List<GroupKpiAttributeMapping> results = jdbcTemplate.query(sql, (rs, rowNum) -> {
                GroupKpiAttributeMapping mapping = new GroupKpiAttributeMapping();
                mapping.setAttributeValue(rs.getString("attributeValue"));
                mapping.setKpiId(rs.getInt("kpiId"));
                mapping.setAliasName(rs.getString("aliasName"));
                return mapping;
            }, accountId, instanceId);

            log.debug("Successfully fetched {} group kpi attribute mapping details for accountId [{}] and instanceId [{}]",
                     results.size(), accountId, instanceId);
            return results;

        } catch (Exception e) {
            log.error("Error while fetching group kpi attribute mapping details for accountId [{}] and instanceId [{}]",
                     accountId, instanceId, e);
            throw new HealControlCenterException("Error while fetching group kpi attribute mapping details for accountId [" +
                                               accountId + "] and instanceId [" + instanceId + "]");
        }
    }

    @SqlQuery("select distinct ci1.id hostInstanceId ,ci1.name hostInstanceName,  ci1.is_DR isDR from " +
            "component_cluster_mapping ccm, comp_instance ci1, comp_instance ci2  where ci1.status = 1 and " +
            "ci1.mst_component_type_id = 1 and  ccm.comp_instance_id=ci1.id and ccm.cluster_id=ci2.id and " +
            " ci2.is_cluster= 1 and ci1.is_cluster = 0 and ci1.mst_component_type_id = :hostCompTypeId and  " +
            "ci1.host_address=:hostAddress and ci1.account_id =:accId and ci1.is_DR=:isDR")
    List<HostInstanceDetails> getHostInstanceId(@Bind("hostCompTypeId") int hostCompTypeId,
                                                @Bind("hostAddress") String hostAddress, @Bind("accId") int accId,
                                                @Bind("isDR") int isDR);
}
