package com.heal.controlcenter.pojo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * POJO for instance cluster service details.
 * Used for cluster component validation at service level.
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class InstanceClusterServicePojo {
    private int clusterId;
    private String clusterName;
    private String clusterIdentifier;
    private int serviceId;
    private String serviceName;
    private String serviceIdentifier;
    private int clusterComponentId;
    private String clusterComponentName;
    private int clusterComponentTypeId;
    private String clusterComponentTypeName;
    private int clusterComponentVersionId;
    private String clusterComponentVersionName;
    private int clusterCommonVersionId;
    private String clusterCommonVersionName;
    private int instanceId;
    private String instanceName;
    private String instanceIdentifier;
    private String hostAddress;
    //    private String monitorPort;
    private int instanceComponentId;
    private String instanceComponentName;
    private int instanceComponentTypeId;
    private String instanceComponentTypeName;
    private int instanceComponentVersionId;
    private String instanceComponentVersionName;
    private int instanceCommonVersionId;
    private String instanceCommonVersionName;
}
