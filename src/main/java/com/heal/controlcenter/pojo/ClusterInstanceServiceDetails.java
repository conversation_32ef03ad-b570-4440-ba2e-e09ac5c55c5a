package com.heal.controlcenter.pojo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * POJO for cluster instance service details.
 * Maps the complex query result that includes cluster, instance, and service information.
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ClusterInstanceServiceDetails {
    
    // Cluster details
    private int clusterId;
    private String clusterName;
    private String clusterIdentifier;
    private int serviceId;
    private String serviceName;
    private String serviceIdentifier;
    private int clusterComponentId;
    private String clusterComponentName;
    private int clusterComponentTypeId;
    private String clusterComponentTypeName;
    private int clusterComponentVersionId;
    private String clusterComponentVersionName;
    private int clusterCommonVersionId;
    private String clusterCommonVersionName;
    
    // Instance details
    private int instanceId;
    private String instanceName;
    private String instanceIdentifier;
    private String hostAddress;
    private int instanceComponentId;
    private String instanceComponentName;
    private int instanceComponentTypeId;
    private String instanceComponentTypeName;
    private int instanceComponentVersionId;
    private String instanceComponentVersionName;
    private int instanceCommonVersionId;
    private String instanceCommonVersionName;
}
